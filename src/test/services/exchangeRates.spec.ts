import { expect, use } from "chai";
import chaiAsPromised from "chai-as-promised";
import { stub } from "sinon";
import { getExchangeRateModel } from "../../skywind/models/exchangeRates";
import { ExchangeRatesUpdateJob } from "../../skywind/services/updateJob";
import { getRates, getRatesRange, loadRates } from "../../skywind/services/exchangeRates";
import { getNextDay, getPrevDay, getTimestamp } from "../../skywind/providers/utils";
import * as Errors from "../../skywind/errors";
import config from "../../skywind/config";
import { Provider } from "../../skywind/providers/provider";
import { ExchangeRateType } from "@skywind-group/sw-currency-exchange";
import { logging } from "@skywind-group/sw-utils";
import { DefaultCurrencyProvider } from "../../skywind/providers/defaultCurrencyProvider";
import * as updateJobModule from "../../skywind/services/updateJob";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

use(chaiAsPromised);

describe("Exchange Rates Service", () => {
    const originalConfig = config.provider;
    let updateJob: ExchangeRatesUpdateJob;
    let initUpdateJobStub: any;

    before(async () => {
        config.provider = Provider.DEFAULT;
        await getExchangeRateModel().sync();
        await getExchangeRateModel().truncate();
        updateJob = new ExchangeRatesUpdateJob(new DefaultCurrencyProvider());
        await updateJob.init();

        // Mock initUpdateJob to return our test updateJob instance
        initUpdateJobStub = stub(updateJobModule, "initUpdateJob").resolves(updateJob);
    });

    after(() => {
        config.provider = originalConfig;
        updateJob.clean();
        initUpdateJobStub.restore();
    });

    it("get rates by date with filter", async () => {
        const today = new Date();
        const rates = await getRates(today);
        expect(rates).to.exist;
        expect(rates.date).to.equal(getTimestamp(today));
        expect(rates.rates.length).to.equal(171);
    });

    it("get rates by date with filter", async () => {
        const today = new Date();
        const rates = await getRates(today, { type: ExchangeRateType.BID, from: "USD", to: "EUR" });
        expect(rates).to.exist;
        expect(rates.date).to.equal(getTimestamp(today));
        expect(rates.rates.length).to.equal(1);
    });

    it("get rates by date - not found", async () => {
        await expect(getRates(new Date("1970-01-01"))).to.be.rejectedWith(Errors.ExchangeRatesNotFoundError);
    });

    it("get rates for date range", async () => {
        const today = new Date();
        const tomorrow = getNextDay();
        const rates = await getRatesRange(today, tomorrow);
        expect(rates.length).to.equal(2);
        expect(rates[0].date).to.equal(getTimestamp(today));
        expect(rates[0].rates.length).to.equal(171);
        expect(rates[1].date).to.equal(getTimestamp(tomorrow));
        expect(rates[1].rates.length).to.equal(171);
    });

    it("get rates for date range with filter", async () => {
        const today = new Date();
        const tomorrow = getNextDay();
        const rates = await getRatesRange(today, tomorrow, { type: ExchangeRateType.BID, from: "USD", to: "EUR" });
        expect(rates.length).to.equal(2);
        expect(rates[0].date).to.equal(getTimestamp(today));
        expect(rates[0].rates.length).to.equal(1);
        expect(rates[1].date).to.equal(getTimestamp(tomorrow));
        expect(rates[1].rates.length).to.equal(1);
    });

    it("load missing rates for date range", async () => {
        const startDate = getPrevDay(new Date(), 5);
        const endDate = new Date();
        let rates = await loadRates(startDate, endDate);
        expect(rates.length).to.equal(12);
        expect(rates[0].date).to.equal(getTimestamp(startDate));
        expect(rates[0].rates.length).to.equal(171);
        expect(rates[11].date).to.equal(getTimestamp(endDate));
        expect(rates[11].rates.length).to.equal(171);

        rates = await loadRates(startDate, endDate, ["USD", "BYN", "GBP"]);
        expect(rates.length).to.equal(12);
        expect(rates[0].date).to.equal(getTimestamp(startDate));
        expect(rates[0].rates.length).to.equal(171);
        expect(rates[11].date).to.equal(getTimestamp(endDate));
        expect(rates[11].rates.length).to.equal(171);
    }).timeout(10000);
});
