import { expect } from "chai";
import { OXRCurrencyDatasource } from "../../skywind/datasources/OXRCurrencyDatasource";
import config from "../../skywind/config";
import { Provider } from "../../skywind/providers/provider";
import { logging } from "@skywind-group/sw-utils";
import * as superagent from "superagent";
const superagentMock = require("superagent-mock");

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("OXR Currency Datasource", () => {
    const originalConfig = config.oxr;
    let mock: any;

    const mockResponseData = {
        "timestamp": **********,
        "base": "USD",
        "rates": {
            "EUR": 0.861603,
            "CNY": 6.83090,
            "VND": 23227.4
        }
    };

    before(() => {
        config.oxr.appKey = "test";
    });

    after(() => {
        config.oxr = originalConfig;
    });

    afterEach(() => {
        if (mock) {
            mock.unset();
        }
    });

    it("get rates", async () => {
        const settings = [{
            fixtures: () => {
                return mockResponseData;
            },
            get: (match: string[], data: any) => ({ statusCode: 200, body: data })
        }];

        mock = superagentMock(superagent, settings);

        const provider = new OXRCurrencyDatasource();
        const today = new Date();
        const rates = await provider.load(today, ["USD"]);
        expect(rates).to.deep.equal({
            "provider": Provider.OXR,
            "bidRates": {
                "USD": {
                    "CNY": 6.8309,
                    "EUR": 0.861603,
                    "VND": 23227.4
                }
            },
            "askRates": {
                "USD": {
                    "CNY": 6.8309,
                    "EUR": 0.861603,
                    "VND": 23227.4
                }
            },
            "ts": today
        });
    });
});
