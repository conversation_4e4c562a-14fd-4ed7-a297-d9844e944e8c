{"name": "@skywind-group/sw-currency-exchange-api", "version": "5.55.0", "private": true, "main": "lib/skywind/app.js", "scripts": {"clean": "rm -rf lib", "copy": "cpx src/**/*.json lib", "version": "mkdir -p lib/skywind && echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version", "compile": "npm run copy && tsc", "only-test": "mocha src/test/**/*.spec.ts", "test": "nyc mocha -r dotenv/config --enable-source-maps --exit --timeout 0 -- lib/test/**/*.spec.js", "lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "start": "npm run compile && npm run version && node -r dotenv/config lib/skywind/app.js"}, "dependencies": {"@fastify/compress": "8.1.0", "@fastify/middie": "9.0.3", "@fastify/type-provider-json-schema-to-ts": "5.0.0", "@skywind-group/gelf-stream": "1.2.6", "@skywind-group/sw-currency-exchange": "2.3.19", "@skywind-group/sw-utils": "2.3.9", "@skywind-group/sw-wallet-adapter-core": "2.1.3", "agentkeepalive": "^4.6.0", "bole": "5.0.20", "bole-console": "0.1.10", "emitter-listener": "1.1.2", "express-prom-bundle": "8.0.0", "fastify": "5.5.0", "jsonwebtoken": "9.0.2", "kafka-node": "5.0.0", "measured-core": "^2.0.0", "node-schedule": "2.1.1", "pg": "8.16.3", "prom-client": "15.0.0", "sequelize": "6.37.7"}, "devDependencies": {"@testdeck/mocha": "0.3.3", "@types/chai": "5.2.2", "@types/chai-as-promised": "8.0.2", "@types/lodash": "4.17.16", "@types/mocha": "10.0.10", "@types/node": "22.15.19", "@types/node-schedule": "2.1.7", "@types/sequelize": "4.27.33", "@types/sinon": "17.0.4", "@types/supertest": "6.0.3", "@types/validator": "13.15.0", "@typescript-eslint/eslint-plugin": "8.32.1", "@typescript-eslint/parser": "8.32.1", "chai": "6.0.0", "chai-as-promised": "8.0.1", "cpx": "1.5.0", "dotenv": "17.2.1", "eslint": "9.33.0", "mocha": "11.7.1", "nyc": "17.1.0", "reflect-metadata": "^0.2.2", "sinon": "21.0.0", "superagent-mock": "^5.0.1", "supertest": "7.1.4", "ts-node": "10.9.2", "typescript": "5.9.2"}}